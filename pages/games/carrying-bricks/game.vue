<template>
    <view class="content relative">
        <!-- 无限滚动背景 -->
        <view class="background-container">
            <view
                v-for="(bg, index) in backgrounds"
                :key="index"
                class="background-layer"
                :style="{backgroundImage: `url(${images.bgImg})`, transform: `translateY(${bg.y}px)`}"
            ></view>
        </view>

        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ distance }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD"
                         @startGame="gameTipsClose"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏主区域 -->
        <view class="game-area" v-if="gameStatus === 1">

            <!-- 角色 -->
            <view class="runner" :class="[isRunning ? 'running' : '', `swing-${swingDirection}`]">
                <image :src="images.roleImg" mode="widthFix"></image>
            </view>

            <!-- 控制按钮 -->
            <view class="control-buttons">
                <view class="control-btn" :class="{ active: nextAction === 'left' }" @tap="handleLeftClick">
                    <image :src="images.leftBtnImg" mode="scaleToFill"></image>
                </view>
                <view
                    class="control-btn"
                    :class="{ active: nextAction === 'right' }"
                    @tap="handleRightClick"
                >
                    <image :src="images.rightBtnImg" mode="scaleToFill"></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import config from './config.js'

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            // 配置文件
            images: config.images,
            config,

            // 游戏状态：0-未开始，1-游戏中，2-已结束
            gameStatus: 0,

            // 屏幕尺寸
            windowHeight: 0,

            // 游戏基础数据
            distance: 0,
            nextAction: 'left',
            isRunning: false,
            swingDirection: '', // 角色摆动方向：'left', 'right', ''

            // 背景数据
            backgrounds: [],
            backgroundOffset: 0, // 当前背景偏移量

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                `🎯 在${this.seconds}秒内通过交替点击按钮让角色前进`,
                `⚡ 左右按钮必须交替点击才能前进`,
                `📊 每前进一步获得1米距离`,
                `🏆 看看你能跑多远！`
            ]
        }
    },

    async onLoad(params) {
        // this.active_id = params.active_id
        // this.point_id = params.point_id
        // this.unit = params.unit
        // this.per_integral = Number(params.per_integral)
        // this.seconds = Number(params.seconds)
        // this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()

        this.$refs.gameTipsPopup.open()
    },

    onUnload() {
        this.clearAllTimers()
    },

    methods: {
        // 初始化系统信息
        init() {
            this.windowHeight = uni.getWindowInfo().windowHeight
            this.initBackgrounds()
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.initGame()
            this.startGameLoop()
        },

        // 初始化游戏数据
        initGame() {
            this.countdown = this.seconds
            this.distance = 0
            this.nextAction = 'left'
            this.isRunning = false
            this.swingDirection = ''
            this.backgroundOffset = 0
            this.clearAllTimers()
            this.initBackgrounds()
        },


        // 开始游戏循环
        startGameLoop() {
            this.gameTimer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.endGame()
                }
            }, 1000)
        },

        // 结束游戏
        endGame() {
            this.gameStatus = 2
            this.clearAllTimers()
        },

        // 清理所有定时器
        clearAllTimers() {
            const timers = [this.gameTimer, this.runningTimer, this.gameLoopTimer]
            timers.forEach((timer) => {
                if (timer) {
                    clearInterval(timer)
                    clearTimeout(timer)
                }
            })
            this.gameTimer = null
            this.runningTimer = null
            this.gameLoopTimer = null
        },

        // 左按钮点击
        handleLeftClick() {
            if (this.gameStatus !== 1 || this.nextAction !== 'left') {
                return
            }
            this.moveForward()
            this.nextAction = 'right'
        },

        // 右按钮点击
        handleRightClick() {
            if (this.gameStatus !== 1 || this.nextAction !== 'right') {
                return
            }
            this.moveForward()
            this.nextAction = 'left'
        },

        // 前进动作
        moveForward() {
            this.distance += config.DISTANCE_PER_STEP
            this.isRunning = true

            // 设置角色摆动方向
            this.swingDirection = this.nextAction

            // 移动背景
            this.moveBackground()

            // 跑步和摆动动画持续时间
            if (this.runningTimer) {
                clearTimeout(this.runningTimer)
            }
            this.runningTimer = setTimeout(() => {
                this.isRunning = false
                this.swingDirection = ''
            }, config.ANIMATION_DURATION)
        },


        // 初始化背景图层
        initBackgrounds() {
            this.backgrounds = []
            // 创建4个背景图层用于更好的无限循环覆盖，确保完全无缝
            const layerHeight = this.windowHeight
            const overlap = 10 // 增加重叠像素避免间隙

            for (let i = 0; i < 4; i++) {
                this.backgrounds.push({
                    y: i * (layerHeight - overlap) - layerHeight, // 每层之间有重叠
                    id: i,
                })
            }
        },

        // 移动背景
        moveBackground() {
            const moveDistance = config.BACKGROUND_MOVE_DISTANCE
            this.backgroundOffset += moveDistance

            // 平滑移动背景
            this.animateBackgroundMove(moveDistance)
        },

        // 背景移动动画
        animateBackgroundMove(totalDistance) {
            const duration = config.BACKGROUND_ANIMATION_DURATION
            const frameInterval = 16 // 约60FPS
            const frameCount = Math.floor(duration / frameInterval)
            const movePerFrame = totalDistance / frameCount

            let currentFrame = 0

            const animate = () => {
                if (currentFrame < frameCount && this.gameStatus === 1) {
                    // 移动所有背景图层
                    this.backgrounds.forEach((bg, index) => {
                        bg.y += movePerFrame

                        // 当背景移出屏幕底部时，重新定位到顶部
                        const triggerPoint = this.windowHeight * 1.5 // 降低触发点
                        if (bg.y >= triggerPoint) {
                            // 找到当前最高的背景位置
                            const otherBgs = this.backgrounds.filter((_, i) => i !== index)
                            if (otherBgs.length > 0) {
                                const highestBg = otherBgs.reduce(
                                    (highest, b) => (b.y < highest.y ? b : highest),
                                    otherBgs[0],
                                )

                                // 将背景重新定位到最高背景的上方，确保无缝拼接
                                const overlap = 10 // 增加重叠像素确保无间隙
                                bg.y = highestBg.y - this.windowHeight + overlap
                            } else {
                                // 如果没有其他背景，重置到顶部
                                bg.y = -this.windowHeight
                            }
                        }
                    })

                    currentFrame++
                    setTimeout(animate, frameInterval)
                }
            }

            animate()
        },
    },
}
</script>

<style scoped lang="scss">
// 基础变量
$bg-dark: rgba(0, 0, 0, 0.6);
$bg-overlay: rgba(0, 0, 0, 0.5);
$white: #fff;
$text-primary: #333;
$text-secondary: #666;
$border-radius: 20px;

// 混合器
@mixin center-flex {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin absolute-full {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

// 主容器
.content {
    @include absolute-full;
    overflow: hidden;

    // 背景容器
    .background-container {
        @include absolute-full;
        z-index: 1;
    }

    // 背景图层
    .background-layer {
        @include absolute-full;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        width: 100%;
        height: calc(100vh + 10px); // 增加高度避免间隙
        margin-top: -5px; // 向上偏移确保无缝拼接
    }
}

.game-tips-msg-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    width: 100%;
}

// 游戏区域
.game-area {
    @include absolute-full;
    position: relative;
    z-index: 2; // 确保在背景之上
}

// 角色
.runner {
    position: absolute;
    left: 50%;
    top: 60%;
    transform: translate(-50%, -50%);
    width: 350rpx;
    z-index: 5; // 确保在最上层

    // 角色摆动动画
    &.swing-left {
        animation: swing-left 0.3s ease-out;
    }

    &.swing-right {
        animation: swing-right 0.3s ease-out;
    }

    image {
        width: 100%;
    }
}

// 动画
@keyframes leg-left {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(-20deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

@keyframes leg-right {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

// 角色摆动动画
@keyframes swing-left {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    50% {
        transform: translate(-50%, -50%) rotate(-8deg) scale(1.05);
    }
    100% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
}

@keyframes swing-right {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    50% {
        transform: translate(-50%, -50%) rotate(8deg) scale(1.05);
    }
    100% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
}

// 控制按钮
.control-buttons {
    position: absolute;
    left: 0;
    top: 80%;
    width: 100%;
    z-index: 6; // 确保在最上层
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.control-btn {
    width: 120rpx;
    height: 120rpx;

    &.active {
        transform: scale(1.2);
    }

    image {
        width: 100%;
        height: 100%;
    }
}
</style>
