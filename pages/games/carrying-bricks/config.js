/*
 * @Author: chliang<PERSON> <EMAIL>
 * @Date: 2025-07-24 15:35:00
 * @LastEditors: chliangck <EMAIL>
 * @LastEditTime: 2025-07-29 16:20:44
 * @FilePath: /线上活动王/pages/game/sprint/config.js
 * @Description: 冲刺游戏配置文件
 */

export default {
  // 背景图片
  images: {
    bgImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/bg.jpg', // 跑道背景图
    roleImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/pepole.png',
    leftBtnImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/left.png',
    rightBtnImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/right.png',
    timeImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/catch_doll/countdown.png',
  },

  // 游戏基本设置
  GAME_DURATION: 30, // 游戏时长（秒）
  ANIMATION_DURATION: 300, // 跑步动画持续时间（毫秒）

  // 背景移动设置
  BACKGROUND_MOVE_DISTANCE: 300, // 每次点击背景移动的距离（像素）
  BACKGROUND_ANIMATION_DURATION: 300, // 背景移动动画时长（毫秒）

  // 游戏规则
  DISTANCE_PER_STEP: 1, // 每步前进距离（米）

  // 评分等级
  praiseWords: [
    // { min: 150, title: '🏆 超神！你是冲刺之王！' },
    { min: 100, title: '🔥 王者级别！技术超群！' },
    { min: 80, title: '⭐ 太棒了！你是冲刺大师！' },
    { min: 50, title: '👍 不错的表现！继续努力！' },
    { min: 30, title: '💪 还不错，再试试！' },
    { min: 10, title: '🎯 多练习就能跑得更远！' },
  ],
}
