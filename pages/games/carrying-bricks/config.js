/*
 * @Author: chliang<PERSON> <EMAIL>
 * @Date: 2025-07-24 15:35:00
 * @LastEditors: chliangck <EMAIL>
 * @LastEditTime: 2025-07-29 16:20:44
 * @FilePath: /线上活动王/pages/game/sprint/config.js
 * @Description: 冲刺游戏配置文件
 */

export default {
  // 背景图片
  images: {
    bgImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/bg.jpg', // 跑道背景图
    roleImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/pepole.png',
    leftBtnImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/left.png',
    rightBtnImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/run/right.png',
    timeImg:
      'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/catch_doll/countdown.png',
  },

  // 游戏基本设置
  GAME_DURATION: 30, // 游戏时长（秒）
  ANIMATION_DURATION: 300, // 跑步动画持续时间（毫秒）

  // 背景移动设置
  BACKGROUND_MOVE_DISTANCE: 300, // 每次点击背景移动的距离（像素）
  BACKGROUND_ANIMATION_DURATION: 300, // 背景移动动画时长（毫秒）

  // 游戏规则
  DISTANCE_PER_STEP: 1, // 每步前进距离（米）

  // 里程碑赞美词配置
  praiseWords: [
    { min: 100, title: '🏆 百米冲刺！你是真正的王者！' },
    { min: 80, title: '⚡ 八十米达成！速度惊人！' },
    { min: 60, title: '🔥 六十米突破！势不可挡！' },
    { min: 40, title: '💪 四十米里程碑！继续加油！' },
    { min: 20, title: '🎯 二十米达成！节奏很棒！' },
    { min: 10, title: '👍 十米开门红！好的开始！' },
  ],
}
